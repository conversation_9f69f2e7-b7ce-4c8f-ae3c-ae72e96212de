<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-900 text-white p-8">
    <h1 class="text-2xl mb-4">Debug Test</h1>
    
    <div class="mb-4">
        <textarea 
            id="user-input" 
            placeholder="Введите ваш запрос..." 
            class="w-full bg-gray-800 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            rows="2"
        ></textarea>
    </div>
    
    <div class="mb-4">
        <button 
            id="send-button" 
            class="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed rounded-lg transition-colors flex items-center justify-center"
        >
            <span id="send-text">Отправить</span>
            <div id="send-loader" class="hidden w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin ml-2"></div>
        </button>
    </div>
    
    <div id="debug-output" class="bg-gray-800 p-4 rounded-lg">
        <h2 class="text-lg mb-2">Debug Output:</h2>
        <div id="debug-log"></div>
    </div>

    <script>
        function log(message) {
            const debugLog = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            debugLog.innerHTML += `<div class="text-sm text-gray-300">[${timestamp}] ${message}</div>`;
        }

        document.addEventListener('DOMContentLoaded', () => {
            log('DOM loaded');
            
            const userInput = document.getElementById('user-input');
            const sendButton = document.getElementById('send-button');
            const sendText = document.getElementById('send-text');
            const sendLoader = document.getElementById('send-loader');
            
            log(`Elements found: input=${!!userInput}, button=${!!sendButton}, text=${!!sendText}, loader=${!!sendLoader}`);
            
            if (sendButton) {
                sendButton.addEventListener('click', (e) => {
                    e.preventDefault();
                    log('Send button clicked!');
                    
                    const message = userInput.value.trim();
                    log(`Message: "${message}"`);
                    
                    if (!message) {
                        log('Empty message, not sending');
                        return;
                    }
                    
                    // Simulate sending
                    sendButton.disabled = true;
                    sendText.style.display = 'none';
                    sendLoader.style.display = 'block';
                    
                    log('Simulating API call...');
                    
                    setTimeout(() => {
                        sendButton.disabled = false;
                        sendText.style.display = 'inline';
                        sendLoader.style.display = 'none';
                        userInput.value = '';
                        log('API call completed (simulated)');
                    }, 2000);
                });
            } else {
                log('ERROR: Send button not found!');
            }
            
            if (userInput) {
                userInput.addEventListener('keydown', (e) => {
                    log(`Key pressed: ${e.key}, shiftKey: ${e.shiftKey}`);
                    
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        log('Enter pressed, triggering send button click');
                        sendButton.click();
                    }
                });
            } else {
                log('ERROR: User input not found!');
            }
        });
    </script>
</body>
</html>
