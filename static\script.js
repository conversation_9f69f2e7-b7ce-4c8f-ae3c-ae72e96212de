// Debug: <PERSON>ript loaded
console.log('AI-IDE script.js loaded successfully');

// API Module for handling all server communications
class APIModule {
    constructor() {
        this.baseURL = '/api';
        this.requestQueue = new Map();
        this.retryAttempts = 3;
        this.retryDelay = 1000;
        this.debounceDelay = 300;
        this.debounceTimers = new Map();
    }

    // Debounced request wrapper
    debounce(key, func, delay = this.debounceDelay) {
        return (...args) => {
            if (this.debounceTimers.has(key)) {
                clearTimeout(this.debounceTimers.get(key));
            }
            
            const timer = setTimeout(() => {
                this.debounceTimers.delete(key);
                func.apply(this, args);
            }, delay);
            
            this.debounceTimers.set(key, timer);
        };
    }

    // Generic request handler with retry logic
    async makeRequest(url, options = {}, retryCount = 0) {
        const requestId = `${options.method || 'GET'}_${url}_${Date.now()}`;
        
        // Prevent duplicate requests
        if (this.requestQueue.has(requestId)) {
            return this.requestQueue.get(requestId);
        }

        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            ...options
        };

        const requestPromise = fetch(url, defaultOptions)
            .then(async response => {
                this.requestQueue.delete(requestId);
                
                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    throw new APIError(
                        errorData.error || 'Request failed',
                        errorData.message || `HTTP ${response.status}`,
                        response.status,
                        errorData
                    );
                }
                
                return response.json();
            })
            .catch(async error => {
                this.requestQueue.delete(requestId);
                
                // Retry logic for network errors
                if (retryCount < this.retryAttempts && this.shouldRetry(error)) {
                    await this.delay(this.retryDelay * Math.pow(2, retryCount));
                    return this.makeRequest(url, options, retryCount + 1);
                }
                
                throw error;
            });

        this.requestQueue.set(requestId, requestPromise);
        return requestPromise;
    }

    // Check if error should trigger a retry
    shouldRetry(error) {
        if (error instanceof APIError) {
            // Retry on server errors (5xx) but not client errors (4xx)
            return error.status >= 500;
        }
        // Retry on network errors
        return error.name === 'TypeError' || error.message.includes('fetch');
    }

    // Utility delay function
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Send chat message
    async sendMessage(message, includeHistory = true) {
        return this.makeRequest(`${this.baseURL}/chat`, {
            method: 'POST',
            body: JSON.stringify({
                message: message.trim(),
                include_history: includeHistory
            })
        });
    }

    // Get chat history
    async getHistory() {
        return this.makeRequest(`${this.baseURL}/history`);
    }

    // Clear chat history
    async clearHistory() {
        return this.makeRequest(`${this.baseURL}/clear-history`, {
            method: 'POST'
        });
    }

    // Cancel all pending requests
    cancelAllRequests() {
        this.requestQueue.clear();
        this.debounceTimers.forEach(timer => clearTimeout(timer));
        this.debounceTimers.clear();
    }
}

// Custom error class for API errors
class APIError extends Error {
    constructor(type, message, status, data = {}) {
        super(message);
        this.name = 'APIError';
        this.type = type;
        this.status = status;
        this.data = data;
    }
}

// Notification system for user feedback
class NotificationSystem {
    constructor() {
        this.container = null;
        this.notifications = new Set();
        this.maxNotifications = 5;
        this.defaultDuration = 5000;
        this.init();
    }

    init() {
        // Create notification container if it doesn't exist
        this.container = document.getElementById('notification-container');
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.id = 'notification-container';
            this.container.className = 'fixed top-4 right-4 z-50 space-y-2 max-w-sm';
            document.body.appendChild(this.container);
        }
    }

    show(message, type = 'info', duration = this.defaultDuration) {
        // Remove oldest notification if we have too many
        if (this.notifications.size >= this.maxNotifications) {
            const oldest = this.notifications.values().next().value;
            this.remove(oldest);
        }

        const notification = this.createNotification(message, type);
        this.notifications.add(notification);
        this.container.appendChild(notification);

        // Animate in
        requestAnimationFrame(() => {
            notification.classList.add('notification-show');
        });

        // Auto-remove after duration
        if (duration > 0) {
            setTimeout(() => {
                this.remove(notification);
            }, duration);
        }

        return notification;
    }

    createNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type} transform translate-x-full transition-transform duration-300 ease-in-out`;
        
        const icons = {
            success: `<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                      </svg>`,
            error: `<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                     <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                   </svg>`,
            warning: `<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                       <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                     </svg>`,
            info: `<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                  </svg>`
        };

        notification.innerHTML = `
            <div class="flex items-start space-x-3 p-4 rounded-lg shadow-lg bg-gray-800 border border-gray-700">
                <div class="notification-icon flex-shrink-0 text-${this.getTypeColor(type)}-400">
                    ${icons[type] || icons.info}
                </div>
                <div class="flex-1 min-w-0">
                    <div class="notification-message text-sm text-white break-words">${message}</div>
                </div>
                <button class="notification-close flex-shrink-0 text-gray-400 hover:text-white transition-colors">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            </div>
        `;

        // Add close functionality
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            this.remove(notification);
        });

        return notification;
    }

    getTypeColor(type) {
        const colors = {
            success: 'green',
            error: 'red',
            warning: 'yellow',
            info: 'blue'
        };
        return colors[type] || 'blue';
    }

    remove(notification) {
        if (!this.notifications.has(notification)) return;

        this.notifications.delete(notification);
        notification.classList.remove('notification-show');
        notification.classList.add('translate-x-full');

        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }

    clear() {
        this.notifications.forEach(notification => {
            this.remove(notification);
        });
    }
}

document.addEventListener('DOMContentLoaded', () => {
    console.log('DOMContentLoaded event fired');

    // Initialize API and notification systems
    const api = new APIModule();
    const notifications = new NotificationSystem();

    console.log('API and notification systems initialized');
    
    // Chat elements
    const chatMessages = document.getElementById('chat-messages');
    const userInput = document.getElementById('user-input');
    const sendButton = document.getElementById('send-button');
    const sendText = document.getElementById('send-text');
    const sendLoader = document.getElementById('send-loader');
    const newChatBtn = document.getElementById('new-chat');

    // Debug: Check if elements are found
    console.log('DOM elements found:', {
        chatMessages: !!chatMessages,
        userInput: !!userInput,
        sendButton: !!sendButton,
        sendText: !!sendText,
        sendLoader: !!sendLoader,
        newChatBtn: !!newChatBtn
    });
    
    // Canvas elements
    const canvasContainer = document.getElementById('canvas-container');
    const codeDisplay = document.getElementById('code-display');
    const canvasWelcome = document.getElementById('canvas-welcome');
    const copyAllCodeBtn = document.getElementById('copy-all-code');
    const saveCodeBtn = document.getElementById('save-code');
    
    // Fullscreen elements
    const fullscreenChatBtn = document.getElementById('fullscreen-chat');
    const fullscreenOverlay = document.getElementById('fullscreen-overlay');
    const exitFullscreenBtn = document.getElementById('exit-fullscreen');
    const fullscreenMessages = document.getElementById('fullscreen-messages');
    const fullscreenInput = document.getElementById('fullscreen-input');
    const fullscreenSendBtn = document.getElementById('fullscreen-send');
    
    // State
    let currentCode = '';
    let currentLanguage = 'plaintext';
    let isFullscreen = false;
    
    // Initialize marked.js options for better markdown rendering
    marked.setOptions({
        highlight: function(code, lang) {
            if (lang && hljs.getLanguage(lang)) {
                try {
                    return hljs.highlight(code, { language: lang }).value;
                } catch (err) {}
            }
            return hljs.highlightAuto(code).value;
        },
        breaks: true,
        gfm: true
    });
    
    // Connection status monitoring
    let isOnline = navigator.onLine;
    let connectionCheckInterval;
    const connectionStatus = document.getElementById('connection-status');
    const connectionText = document.getElementById('connection-text');

    function updateConnectionStatus() {
        const wasOnline = isOnline;
        isOnline = navigator.onLine;
        
        if (connectionStatus) {
            if (isOnline) {
                connectionStatus.className = 'connection-status online';
                if (connectionText) connectionText.textContent = 'Подключено';
                
                if (!wasOnline) {
                    notifications.show('Соединение восстановлено', 'success', 3000);
                    retryFailedRequests();
                    
                    // Hide status indicator after a delay
                    setTimeout(() => {
                        connectionStatus.classList.add('hidden');
                    }, 3000);
                }
            } else {
                connectionStatus.className = 'connection-status offline';
                connectionStatus.classList.remove('hidden');
                if (connectionText) connectionText.textContent = 'Нет подключения';
                
                if (wasOnline) {
                    notifications.show('Соединение потеряно', 'warning', 0); // Don't auto-hide
                }
            }
        }
    }

    function retryFailedRequests() {
        // Clear any existing retry timers and attempt to reload history
        setTimeout(() => {
            loadChatHistory();
        }, 1000);
    }

    // Show connection status temporarily
    function showConnectionStatus(status, message, duration = 3000) {
        if (connectionStatus && connectionText) {
            connectionStatus.className = `connection-status ${status}`;
            connectionStatus.classList.remove('hidden');
            connectionText.textContent = message;
            
            if (duration > 0) {
                setTimeout(() => {
                    connectionStatus.classList.add('hidden');
                }, duration);
            }
        }
    }

    // Monitor connection status
    window.addEventListener('online', updateConnectionStatus);
    window.addEventListener('offline', updateConnectionStatus);

    // Periodic connection check
    connectionCheckInterval = setInterval(() => {
        if (!navigator.onLine && isOnline) {
            updateConnectionStatus();
        }
    }, 5000);

    // Enhanced event listeners with debouncing and error handling
    if (sendButton) {
        sendButton.addEventListener('click', (e) => {
            e.preventDefault();
            console.log('Send button clicked');
            sendMessage(false);
        });
    } else {
        console.error('Send button not found!');
    }

    if (userInput) {
        userInput.addEventListener('keydown', handleInputKeydown);
    } else {
        console.error('User input not found!');
    }

    if (newChatBtn) {
        newChatBtn.addEventListener('click', clearHistory);
    }

    if (fullscreenChatBtn) {
        fullscreenChatBtn.addEventListener('click', enterFullscreen);
    }

    if (exitFullscreenBtn) {
        exitFullscreenBtn.addEventListener('click', exitFullscreen);
    }

    if (fullscreenSendBtn) {
        fullscreenSendBtn.addEventListener('click', (e) => {
            e.preventDefault();
            console.log('Fullscreen send button clicked');
            sendMessage(true);
        });
    }

    if (fullscreenInput) {
        fullscreenInput.addEventListener('keydown', (e) => handleInputKeydown(e, true));
    }

    if (copyAllCodeBtn) {
        copyAllCodeBtn.addEventListener('click', copyAllCode);
    }

    if (saveCodeBtn) {
        saveCodeBtn.addEventListener('click', saveCode);
    }

    // Cleanup on page unload
    window.addEventListener('beforeunload', () => {
        api.cancelAllRequests();
        clearInterval(connectionCheckInterval);
        notifications.clear();
    });
    
    // Keyboard shortcuts for fullscreen mode
    document.addEventListener('keydown', handleGlobalKeydown);
    
    // Handle fullscreen overlay clicks (close on backdrop click)
    fullscreenOverlay.addEventListener('click', (e) => {
        if (e.target === fullscreenOverlay) {
            exitFullscreen();
        }
    });
    
    // Set loading state for buttons
    function setLoadingState(loading) {
        const buttons = [sendButton, fullscreenSendBtn];
        const texts = [sendText, fullscreenSendBtn?.querySelector('span')];
        
        buttons.forEach((btn, index) => {
            if (btn) {
                btn.disabled = loading;
                if (loading) {
                    btn.classList.add('button-loading');
                } else {
                    btn.classList.remove('button-loading');
                }
            }
        });
        
        // Update input states
        [userInput, fullscreenInput].forEach(input => {
            if (input) {
                input.disabled = loading;
                if (loading) {
                    input.classList.add('opacity-50');
                } else {
                    input.classList.remove('opacity-50');
                }
            }
        });
    }

    // Load chat history with error handling
    async function loadChatHistory() {
        try {
            const data = await api.getHistory();
            
            if (data.messages && data.messages.length > 0) {
                // Clear existing messages
                chatMessages.innerHTML = '';
                fullscreenMessages.innerHTML = '';
                
                // Add messages to chat
                data.messages.forEach(msg => {
                    addMessage(msg.content, msg.role, msg.code, msg.language);
                });
                
                // Update canvas with latest code
                const lastCodeMessage = data.messages
                    .reverse()
                    .find(msg => msg.code && msg.code.trim());
                
                if (lastCodeMessage) {
                    updateCanvas(lastCodeMessage.code, lastCodeMessage.language);
                }
            }
        } catch (error) {
            console.error('Failed to load chat history:', error);
            notifications.show('Не удалось загрузить историю чата', 'warning', 3000);
        }
    }

    // Clear history with confirmation
    async function clearHistory() {
        if (!confirm('Вы уверены, что хотите очистить историю чата?')) {
            return;
        }
        
        try {
            await api.clearHistory();
            
            // Clear UI
            chatMessages.innerHTML = '<div class="text-center text-gray-500 py-8"><svg class="w-12 h-12 mx-auto mb-4 opacity-50" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clip-rule="evenodd"></path></svg><p class="text-sm">Начните диалог с ИИ-помощником</p></div>';
            fullscreenMessages.innerHTML = '';
            
            // Reset canvas
            canvasWelcome.style.display = 'flex';
            codeDisplay.innerHTML = '';
            currentCode = '';
            currentLanguage = 'plaintext';
            
            notifications.show('История чата очищена', 'success', 3000);
        } catch (error) {
            console.error('Failed to clear history:', error);
            notifications.show('Не удалось очистить историю', 'error', 3000);
        }
    }

    // Scroll to bottom of chat
    function scrollToBottom() {
        setTimeout(() => {
            chatMessages.scrollTop = chatMessages.scrollHeight;
            fullscreenMessages.scrollTop = fullscreenMessages.scrollHeight;
        }, 100);
    }

    // Load chat history on page load
    loadChatHistory();
    
    // Handle window resize events
    window.addEventListener('resize', handleWindowResize);
    
    // Handle window resize
    function handleWindowResize() {
        if (isFullscreen) {
            // Ensure fullscreen overlay covers the entire viewport
            setTimeout(() => {
                scrollToBottom();
            }, 100);
        }
    }
    
    // Enhanced input handling with validation and debouncing
    function handleInputKeydown(e, isFullscreen = false) {
        const input = isFullscreen ? fullscreenInput : userInput;

        console.log('handleInputKeydown called:', { key: e.key, shiftKey: e.shiftKey, isFullscreen });

        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();

            // Validate input before sending
            const message = input.value.trim();
            console.log('Enter pressed, message:', message);

            if (!message) {
                notifications.show('Введите сообщение', 'warning', 2000);
                return;
            }

            if (message.length > 4000) {
                notifications.show('Сообщение слишком длинное (максимум 4000 символов)', 'warning', 3000);
                return;
            }

            if (!isOnline) {
                notifications.show('Нет подключения к интернету', 'error', 3000);
                return;
            }

            console.log('Calling sendMessage from Enter key');
            sendMessage(isFullscreen);
        }

        // Handle Escape key to cancel current request
        if (e.key === 'Escape' && sendButton.disabled) {
            api.cancelAllRequests();
            setLoadingState(false);
            notifications.show('Запрос отменен', 'info', 2000);
        }
    }

    // Input validation and character counter
    function setupInputValidation() {
        [userInput, fullscreenInput].forEach(input => {
            let timeoutId;
            
            input.addEventListener('input', () => {
                clearTimeout(timeoutId);
                timeoutId = setTimeout(() => {
                    const length = input.value.length;
                    const maxLength = 4000;
                    
                    // Show character count warning when approaching limit
                    if (length > maxLength * 0.8) {
                        const remaining = maxLength - length;
                        if (remaining > 0) {
                            notifications.show(`Осталось символов: ${remaining}`, 'warning', 2000);
                        } else {
                            notifications.show('Превышен лимит символов', 'error', 3000);
                        }
                    }
                }, 1000);
            });
        });
    }

    // Initialize input validation
    setupInputValidation();

    // Request queue manager for handling multiple simultaneous requests
    class RequestQueueManager {
        constructor() {
            this.activeRequests = new Set();
            this.maxConcurrentRequests = 3;
        }

        async addRequest(requestPromise, requestId) {
            // Wait if we have too many concurrent requests
            while (this.activeRequests.size >= this.maxConcurrentRequests) {
                await this.delay(100);
            }

            this.activeRequests.add(requestId);
            
            try {
                const result = await requestPromise;
                return result;
            } finally {
                this.activeRequests.delete(requestId);
            }
        }

        delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        cancelAll() {
            this.activeRequests.clear();
        }
    }

    const requestQueue = new RequestQueueManager();

    // Enhanced utility functions
    const utils = {
        // Sanitize HTML to prevent XSS
        sanitizeHTML: (str) => {
            const div = document.createElement('div');
            div.textContent = str;
            return div.innerHTML;
        },

        // Format file size
        formatFileSize: (bytes) => {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        },

        // Truncate text with ellipsis
        truncateText: (text, maxLength) => {
            if (text.length <= maxLength) return text;
            return text.substring(0, maxLength - 3) + '...';
        },

        // Copy text to clipboard with fallback
        copyToClipboard: async (text) => {
            try {
                if (navigator.clipboard && window.isSecureContext) {
                    await navigator.clipboard.writeText(text);
                    return true;
                } else {
                    // Fallback for older browsers
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.position = 'fixed';
                    textArea.style.left = '-999999px';
                    textArea.style.top = '-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    const result = document.execCommand('copy');
                    textArea.remove();
                    return result;
                }
            } catch (err) {
                console.error('Failed to copy text:', err);
                return false;
            }
        },

        // Validate message content
        validateMessage: (message) => {
            const errors = [];
            
            if (!message || !message.trim()) {
                errors.push('Сообщение не может быть пустым');
            }
            
            if (message.length > 4000) {
                errors.push('Сообщение слишком длинное (максимум 4000 символов)');
            }
            
            // Check for potentially harmful content
            const suspiciousPatterns = [
                /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
                /javascript:/gi,
                /on\w+\s*=/gi
            ];
            
            if (suspiciousPatterns.some(pattern => pattern.test(message))) {
                errors.push('Сообщение содержит недопустимый контент');
            }
            
            return {
                isValid: errors.length === 0,
                errors
            };
        }
    };

    // Performance monitoring
    const performanceMonitor = {
        startTime: null,
        
        start: (operation) => {
            performanceMonitor.startTime = performance.now();
            console.log(`Starting ${operation}...`);
        },
        
        end: (operation) => {
            if (performanceMonitor.startTime) {
                const duration = performance.now() - performanceMonitor.startTime;
                console.log(`${operation} completed in ${duration.toFixed(2)}ms`);
                
                // Show performance warning if operation took too long
                if (duration > 5000) {
                    notifications.show(`${operation} заняло ${(duration/1000).toFixed(1)}с`, 'warning', 3000);
                }
                
                performanceMonitor.startTime = null;
            }
        }
    };

    // Make utilities globally available for other scripts
    window.AIIDEUtils = utils;
    window.AIIDENotifications = notifications;
    window.AIIDEAPI = api;
    
    // Handle global keyboard shortcuts
    function handleGlobalKeydown(e) {
        // F11 or Ctrl+Shift+F to toggle fullscreen
        if (e.key === 'F11' || (e.ctrlKey && e.shiftKey && e.key === 'F')) {
            e.preventDefault();
            if (isFullscreen) {
                exitFullscreen();
            } else {
                enterFullscreen();
            }
        }
        
        // Escape to exit fullscreen
        if (e.key === 'Escape' && isFullscreen) {
            e.preventDefault();
            exitFullscreen();
        }
        
        // Ctrl+Enter to send message in fullscreen
        if (e.ctrlKey && e.key === 'Enter' && isFullscreen) {
            e.preventDefault();
            sendMessage(true);
        }
    }
    
    // Enhanced send message function with improved error handling
    async function sendMessage(isFullscreen = false) {
        const input = isFullscreen ? fullscreenInput : userInput;
        const message = input.value.trim();

        console.log('sendMessage called with:', { isFullscreen, message, disabled: sendButton.disabled });

        if (!message || sendButton.disabled) {
            console.log('Message sending blocked:', { message: !!message, disabled: sendButton.disabled });
            return;
        }

        // Validate message before sending
        const validation = utils.validateMessage(message);
        if (!validation.isValid) {
            validation.errors.forEach(error => {
                notifications.show(error, 'error', 4000);
            });
            return;
        }

        // Add user message to chat immediately
        addMessage(message, 'user');
        input.value = '';
        setLoadingState(true);

        try {
            performanceMonitor.start('Отправка сообщения');

            // Send message to API with retry logic
            const data = await api.sendMessage(message, true);

            performanceMonitor.end('Отправка сообщения');

            // Handle successful response
            if (data.response) {
                addMessage(data.response, 'assistant', data.code, data.language);

                // Update canvas if code is present
                if (data.code) {
                    updateCanvas(data.code, data.language);
                    notifications.show('Код обновлен в холсте', 'success', 3000);
                }

                // Show notification for multiple code blocks
                if (data.code_blocks && data.code_blocks.length > 1) {
                    notifications.show(`Получено ${data.code_blocks.length} блоков кода`, 'info', 3000);
                }

                // Handle fallback responses
                if (data.status === 'fallback') {
                    notifications.show('ИИ недоступен. Показан резервный ответ.', 'warning', 5000);
                    showRetryOption(message);
                }
            }

        } catch (error) {
            console.error('Chat error:', error);
            performanceMonitor.end('Отправка сообщения');

            if (error instanceof APIError) {
                // Handle specific API errors
                handleAPIError(error, message);
            } else {
                // Handle network/unknown errors
                handleNetworkError(error, message);
            }
        } finally {
            setLoadingState(false);
        }
    }

    // Handle API-specific errors
    function handleAPIError(error, originalMessage = '') {
        let title = 'Ошибка API';
        let message = error.message;
        let notificationType = 'error';
        let retryAfter = 0;
        
        switch (error.status) {
            case 400:
                title = 'Некорректный запрос';
                message = 'Проверьте введенное сообщение';
                notificationType = 'warning';
                break;
            case 401:
                title = 'Ошибка авторизации';
                message = 'Проверьте API ключ';
                break;
            case 403:
                title = 'Доступ запрещен';
                message = 'Недостаточно прав для выполнения операции';
                break;
            case 404:
                title = 'Сервис не найден';
                message = 'API endpoint недоступен';
                break;
            case 429:
                title = 'Превышен лимит запросов';
                message = 'Слишком много запросов. Попробуйте позже.';
                retryAfter = error.data?.retry_after || 60;
                notificationType = 'warning';
                showRetryOption(originalMessage, retryAfter);
                break;
            case 500:
            case 502:
            case 503:
            case 504:
                title = 'Ошибка сервера';
                message = 'Сервер временно недоступен';
                retryAfter = error.data?.retry_after || 30;
                notificationType = 'error';
                showRetryOption(originalMessage, retryAfter);
                break;
            default:
                if (error.status >= 500) {
                    title = 'Ошибка сервера';
                    message = 'Внутренняя ошибка сервера. Попробуйте позже';
                    retryAfter = 10;
                } else {
                    title = 'Неизвестная ошибка';
                    message = 'Произошла неизвестная ошибка';
                }
                break;
        }
        
        showError(title, message, originalMessage, retryAfter);
        notifications.show(`${title}: ${message}`, notificationType, 5000);
    }

    // Handle network and unknown errors
    function handleNetworkError(error, originalMessage = '') {
        let title = 'Ошибка сети';
        let message = 'Проверьте подключение к интернету';
        let retryAfter = 5;
        
        if (error.name === 'AbortError') {
            title = 'Запрос отменен';
            message = 'Запрос был отменен пользователем';
            retryAfter = 0;
        } else if (error.message.includes('timeout')) {
            title = 'Превышено время ожидания';
            message = 'Сервер не отвечает. Попробуйте позже';
            retryAfter = 10;
        } else if (error.message.includes('fetch')) {
            title = 'Ошибка подключения';
            message = 'Не удается подключиться к серверу';
            retryAfter = 5;
        }
        
        showError(title, message, originalMessage, retryAfter);
        notifications.show(`${title}: ${message}`, 'error', 5000);
    }

    // Show error with retry option
    function showError(title, message, originalMessage = '', retryAfter = 0) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-display';
        errorDiv.innerHTML = `
            <div class="error-title">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                ${title}
            </div>
            <div class="error-message">${message}</div>
            <div class="error-actions">
                ${originalMessage ? `<button class="error-retry-btn" onclick="retryMessage('${originalMessage.replace(/'/g, "\\'")}')">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"></path>
                    </svg>
                    Повторить${retryAfter > 0 ? ` (${retryAfter}с)` : ''}
                </button>` : ''}
                <button class="error-dismiss-btn" onclick="this.parentElement.parentElement.remove()">
                    Закрыть
                </button>
            </div>
        `;
        
        // Add to chat messages
        chatMessages.appendChild(errorDiv);
        fullscreenMessages.appendChild(errorDiv.cloneNode(true));
        
        // Auto-enable retry button after delay
        if (retryAfter > 0 && originalMessage) {
            const retryBtn = errorDiv.querySelector('.error-retry-btn');
            if (retryBtn) {
                retryBtn.disabled = true;
                let countdown = retryAfter;
                
                const countdownInterval = setInterval(() => {
                    countdown--;
                    retryBtn.textContent = `Повторить (${countdown}с)`;
                    
                    if (countdown <= 0) {
                        clearInterval(countdownInterval);
                        retryBtn.disabled = false;
                        retryBtn.innerHTML = `
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"></path>
                            </svg>
                            Повторить
                        `;
                    }
                }, 1000);
            }
        }
        
        scrollToBottom();
    }

    // Retry message function
    window.retryMessage = function(message) {
        const input = isFullscreen ? fullscreenInput : userInput;
        input.value = message;
        sendMessage(isFullscreen);
    };

    // Show retry option for fallback responses
    function showRetryOption(originalMessage) {
        const retryDiv = document.createElement('div');
        retryDiv.className = 'retry-container bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-4 mt-4';
        retryDiv.innerHTML = `
            <div class="text-yellow-400 font-medium mb-2">ИИ-сервис недоступен</div>
            <div class="text-yellow-300 text-sm mb-3">Показан резервный ответ. Попробуйте повторить запрос позже.</div>
            <button class="retry-button" onclick="retryMessage('${originalMessage.replace(/'/g, "\\'")}')">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"></path>
                </svg>
                Попробовать снова
            </button>
        `;
        
        chatMessages.appendChild(retryDiv);
        fullscreenMessages.appendChild(retryDiv.cloneNode(true));
        scrollToBottom();
    }
    
    // Add message to chat
    function addMessage(content, role, code = null, language = null) {
        const messageDiv = document.createElement('div');
        messageDiv.classList.add('message', `${role}-message`);
        
        // Add timestamp
        const timestamp = new Date().toLocaleTimeString('ru-RU', {
            hour: '2-digit',
            minute: '2-digit'
        });
        
        const timestampDiv = document.createElement('div');
        timestampDiv.classList.add('text-xs', 'text-gray-400', 'mb-2');
        timestampDiv.textContent = `${role === 'user' ? 'Вы' : 'ИИ'} • ${timestamp}`;
        messageDiv.appendChild(timestampDiv);
        
        const contentDiv = document.createElement('div');
        contentDiv.classList.add('message-content');
        
        if (role === 'assistant') {
            // Parse markdown for AI messages
            contentDiv.innerHTML = marked.parse(content);
            
            // Add copy buttons to code blocks
            contentDiv.querySelectorAll('pre').forEach(pre => {
                addCopyButtonToCodeBlock(pre);
            });
        } else {
            // Plain text for user messages
            contentDiv.textContent = content;
        }
        
        messageDiv.appendChild(contentDiv);
        
        // Add to normal chat
        chatMessages.appendChild(messageDiv.cloneNode(true));
        
        // Add to fullscreen chat (with proper event listeners)
        const fullscreenMessageDiv = messageDiv.cloneNode(true);
        if (role === 'assistant') {
            // Re-add copy button event listeners for fullscreen
            fullscreenMessageDiv.querySelectorAll('pre').forEach(pre => {
                const copyButton = pre.querySelector('.copy-button');
                if (copyButton) {
                    addCopyButtonEventListener(copyButton);
                }
            });
        }
        fullscreenMessages.appendChild(fullscreenMessageDiv);
        
        // Highlight code syntax
        hljs.highlightAll();
        
        // Auto-scroll to new messages
        scrollToBottom();
    }
    
    // Add copy button to code blocks
    function addCopyButtonToCodeBlock(pre) {
        const button = document.createElement('button');
        button.classList.add('copy-button');
        button.innerHTML = `
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"></path>
                <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z"></path>
            </svg>
        `;
        button.title = 'Копировать код';
        
        // Use the helper function to add event listener
        addCopyButtonEventListener(button);
        
        pre.style.position = 'relative';
        pre.appendChild(button);
    }
    
    // Update canvas with new code
    function updateCanvas(code, language) {
        currentCode = code;
        currentLanguage = detectLanguage(code, language) || 'plaintext';
        
        if (code && code.trim()) {
            canvasWelcome.style.display = 'none';
            
            // Create canvas content with enhanced code display
            const canvasContent = createCanvasContent(code, currentLanguage);
            codeDisplay.innerHTML = canvasContent;
            
            // Apply syntax highlighting
            hljs.highlightAll();
            
            // Add line numbers if code is multiline
            addLineNumbers();
            
            // Update canvas header with language info
            updateCanvasHeader(currentLanguage);
        } else {
            canvasWelcome.style.display = 'flex';
            codeDisplay.innerHTML = '';
            resetCanvasHeader();
        }
    }
    
    // Create enhanced canvas content
    function createCanvasContent(code, language) {
        const escapedCode = escapeHtml(code);
        const languageClass = `language-${language}`;
        
        return `
            <div class="canvas-code-container">
                <div class="canvas-code-header">
                    <div class="flex items-center space-x-2">
                        <span class="file-extension ${language}">${language.toUpperCase()}</span>
                        <span class="text-sm text-gray-400">${getLineCount(code)} строк</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button class="canvas-copy-btn" onclick="copyCanvasCode()" title="Копировать код">
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"></path>
                                <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
                <pre class="canvas-code-block"><code class="${languageClass}">${escapedCode}</code></pre>
            </div>
        `;
    }
    
    // Detect programming language automatically
    function detectLanguage(code, providedLanguage) {
        if (providedLanguage && providedLanguage !== 'text' && providedLanguage !== 'plaintext') {
            return providedLanguage;
        }
        
        if (!code || !code.trim()) {
            return 'plaintext';
        }
        
        const content = code.toLowerCase();
        
        // CSS detection (check first as it might contain 'class')
        if (isCSS(content)) return 'css';
        
        // Python detection
        if (isPython(content)) return 'python';
        
        // JavaScript detection
        if (isJavaScript(content)) return 'javascript';
        
        // HTML detection
        if (isHTML(content)) return 'html';
        
        // Java detection
        if (isJava(content)) return 'java';
        
        // C++ detection
        if (isCPP(content)) return 'cpp';
        
        // C# detection
        if (isCSharp(content)) return 'csharp';
        
        // SQL detection
        if (isSQL(content)) return 'sql';
        
        // JSON detection
        if (isJSON(content)) return 'json';
        
        // YAML detection
        if (isYAML(content)) return 'yaml';
        
        // Bash detection
        if (isBash(content)) return 'bash';
        
        // TypeScript detection
        if (isTypeScript(content)) return 'typescript';
        
        // PHP detection
        if (isPHP(content)) return 'php';
        
        // Ruby detection
        if (isRuby(content)) return 'ruby';
        
        // Go detection
        if (isGo(content)) return 'go';
        
        return 'plaintext';
    }
    
    // Language detection helper functions
    function isCSS(content) {
        const cssProps = ['display:', 'color:', 'margin:', 'padding:', 'background:', 'font-'];
        const hasCSSProps = cssProps.some(prop => content.includes(prop));
        const hasCSSStructure = content.includes('{') && content.includes('}') && 
                               content.includes(':') && content.includes(';');
        return hasCSSProps && hasCSSStructure;
    }
    
    function isPython(content) {
        const pythonKeywords = ['def ', 'import ', 'class ', 'if __name__', 'print(', 'from '];
        return pythonKeywords.some(keyword => content.includes(keyword));
    }
    
    function isJavaScript(content) {
        const jsKeywords = ['function ', 'var ', 'let ', 'const ', 'console.log', '=>', 'document.'];
        return jsKeywords.some(keyword => content.includes(keyword));
    }
    
    function isHTML(content) {
        const htmlPatterns = ['<!doctype html>', '<html', '<head>', '<body>', '<div', '<span'];
        return htmlPatterns.some(pattern => content.includes(pattern));
    }
    
    function isJava(content) {
        return content.includes('public static void main') && content.includes('class ');
    }
    
    function isCPP(content) {
        const cppPatterns = ['#include', 'std::', 'cout', 'cin', 'int main()'];
        return cppPatterns.some(pattern => content.includes(pattern));
    }
    
    function isCSharp(content) {
        const csharpPatterns = ['using System', 'namespace ', 'public class', 'Console.WriteLine'];
        return csharpPatterns.some(pattern => content.includes(pattern));
    }
    
    function isSQL(content) {
        const sqlKeywords = ['select ', 'from ', 'where ', 'insert ', 'update ', 'delete ', 'create table'];
        const sqlScore = sqlKeywords.filter(keyword => content.includes(keyword)).length;
        return sqlScore >= 2;
    }
    
    function isJSON(content) {
        const trimmed = content.trim();
        return (trimmed.startsWith('{') && trimmed.endsWith('}')) ||
               (trimmed.startsWith('[') && trimmed.endsWith(']'));
    }
    
    function isYAML(content) {
        const yamlPatterns = ['---', '- name:', 'version:', 'dependencies:'];
        const hasYAMLStructure = content.includes(':') && !content.includes(';');
        return hasYAMLStructure && yamlPatterns.some(pattern => content.includes(pattern));
    }
    
    function isBash(content) {
        const bashPatterns = ['#!/bin/bash', '#!/bin/sh', 'echo ', 'export ', 'chmod ', 'mkdir '];
        return bashPatterns.some(pattern => content.includes(pattern));
    }
    
    function isTypeScript(content) {
        const tsPatterns = ['interface ', 'type ', ': string', ': number', ': boolean', 'export interface'];
        return tsPatterns.some(pattern => content.includes(pattern));
    }
    
    function isPHP(content) {
        return content.includes('<?php') || content.includes('<?=');
    }
    
    function isRuby(content) {
        const rubyPatterns = ['def ', 'end', 'puts ', 'require ', 'class ', '@'];
        return rubyPatterns.filter(pattern => content.includes(pattern)).length >= 2;
    }
    
    function isGo(content) {
        const goPatterns = ['package ', 'func ', 'import ', 'fmt.Print'];
        return goPatterns.some(pattern => content.includes(pattern));
    }
    
    // Add line numbers to code display
    function addLineNumbers() {
        const codeBlock = codeDisplay.querySelector('pre code');
        if (!codeBlock) return;
        
        const lines = codeBlock.textContent.split('\n');
        if (lines.length <= 1) return; // Don't add line numbers for single line
        
        const lineNumbersContainer = document.createElement('div');
        lineNumbersContainer.className = 'line-numbers';
        
        const lineNumbers = lines.map((_, index) => 
            `<span class="line-number">${index + 1}</span>`
        ).join('');
        
        lineNumbersContainer.innerHTML = lineNumbers;
        
        const pre = codeBlock.parentElement;
        pre.classList.add('has-line-numbers');
        pre.insertBefore(lineNumbersContainer, codeBlock);
    }
    
    // Update canvas header with language information
    function updateCanvasHeader(language) {
        const header = document.querySelector('.canvas-code-container .canvas-code-header');
        if (header) {
            const languageBadge = header.querySelector('.file-extension');
            if (languageBadge) {
                languageBadge.textContent = language.toUpperCase();
                languageBadge.className = `file-extension ${language}`;
            }
        }
    }
    
    // Reset canvas header
    function resetCanvasHeader() {
        // Canvas header is part of the content, so it gets reset when content is cleared
    }
    
    // Get line count for display
    function getLineCount(code) {
        return code ? code.split('\n').length : 0;
    }
    
    // Copy code from canvas
    function copyCanvasCode() {
        if (currentCode) {
            navigator.clipboard.writeText(currentCode).then(() => {
                const btn = document.querySelector('.canvas-copy-btn');
                if (btn) {
                    const originalHTML = btn.innerHTML;
                    btn.innerHTML = `
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    `;
                    btn.classList.add('copied');
                    setTimeout(() => {
                        btn.innerHTML = originalHTML;
                        btn.classList.remove('copied');
                    }, 2000);
                }
            }).catch(err => {
                console.error('Failed to copy code:', err);
            });
        }
    }
    
    // Make copyCanvasCode globally available
    window.copyCanvasCode = copyCanvasCode;
    
    // Auto-scroll to bottom of chat
    function scrollToBottom() {
        setTimeout(() => {
            chatMessages.scrollTop = chatMessages.scrollHeight;
            if (isFullscreen) {
                fullscreenMessages.scrollTop = fullscreenMessages.scrollHeight;
            }
        }, 100);
    }
    
    // Set loading state
    function setLoadingState(loading) {
        sendButton.disabled = loading;
        fullscreenSendBtn.disabled = loading;
        userInput.disabled = loading;
        fullscreenInput.disabled = loading;
        
        if (loading) {
            sendText.style.display = 'none';
            sendLoader.style.display = 'block';
        } else {
            sendText.style.display = 'inline';
            sendLoader.style.display = 'none';
        }
    }
    
    // Enhanced error display in chat
    function showError(title, message) {
        const errorDiv = document.createElement('div');
        errorDiv.classList.add('error-message', 'bg-red-900', 'border', 'border-red-700', 'rounded-lg', 'p-4', 'mb-4');
        errorDiv.innerHTML = `
            <div class="flex items-start space-x-3">
                <svg class="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                </svg>
                <div class="flex-1">
                    <div class="font-semibold text-red-200">${title}</div>
                    <div class="text-sm mt-1 text-red-300">${message}</div>
                </div>
            </div>
        `;
        
        chatMessages.appendChild(errorDiv.cloneNode(true));
        fullscreenMessages.appendChild(errorDiv);
        scrollToBottom();
    }
    
    // Enhanced clear chat history with better error handling
    async function clearHistory() {
        if (!confirm('Вы уверены, что хотите начать новый чат? Текущая история будет удалена.')) {
            return;
        }

        try {
            const data = await api.clearHistory();
            
            if (data.status === 'success') {
                // Clear chat displays
                chatMessages.innerHTML = `
                    <div class="text-center text-gray-500 py-8">
                        <svg class="w-12 h-12 mx-auto mb-4 opacity-50" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clip-rule="evenodd"></path>
                        </svg>
                        <p class="text-sm">Начните диалог с ИИ-помощником</p>
                    </div>
                `;
                fullscreenMessages.innerHTML = '';
                
                // Reset canvas
                currentCode = '';
                currentLanguage = 'plaintext';
                updateCanvas('', '');
                
                // Show success notification
                notifications.show('История чата очищена', 'success', 3000);
            }
            
        } catch (error) {
            console.error('Error clearing history:', error);
            
            if (error instanceof APIError) {
                showError('Ошибка очистки', error.message);
                notifications.show(`Не удалось очистить историю: ${error.message}`, 'error');
            } else {
                showError('Ошибка сети', 'Проверьте подключение к интернету');
                notifications.show('Ошибка сети при очистке истории', 'error');
            }
        }
    }
    
    // Enhanced load chat history with better error handling
    async function loadChatHistory() {
        try {
            const data = await api.getHistory();
            
            if (data.messages && data.messages.length > 0) {
                // Clear welcome message
                chatMessages.innerHTML = '';
                fullscreenMessages.innerHTML = '';
                
                // Add messages with loading indicator
                let lastCodeMessage = null;
                
                data.messages.forEach(msg => {
                    addMessage(msg.content, msg.role, msg.code, msg.language);
                    
                    // Keep track of the last message with code for canvas update
                    if (msg.code && msg.role === 'assistant') {
                        lastCodeMessage = msg;
                    }
                });
                
                // Update canvas with the most recent code
                if (lastCodeMessage) {
                    updateCanvas(lastCodeMessage.code, lastCodeMessage.language);
                }
                
                // Show history loaded notification
                notifications.show(`Загружено ${data.messages.length} сообщений`, 'info', 2000);
            }
            
        } catch (error) {
            console.error('Error loading history:', error);
            
            // Don't show error notifications for history loading failures
            // as this is not critical and happens on page load
            if (error instanceof APIError && error.status !== 404) {
                console.warn('Failed to load chat history:', error.message);
            }
        }
    }
    
    // Fullscreen functionality
    function enterFullscreen() {
        isFullscreen = true;
        fullscreenOverlay.classList.remove('hidden');
        
        // Sync messages from normal chat to fullscreen
        syncMessagesToFullscreen();
        
        // Sync input content
        fullscreenInput.value = userInput.value;
        
        // Focus on fullscreen input
        setTimeout(() => {
            fullscreenInput.focus();
        }, 100);
        
        // Scroll to bottom
        scrollToBottom();
        
        // Save state for restoration
        saveFullscreenState();
        
        // Show fullscreen indicator
        showFullscreenIndicator('Полноэкранный режим активирован • Нажмите Esc для выхода');
        
        // Update button icon to indicate fullscreen state
        updateFullscreenButtonState(true);
    }
    
    function exitFullscreen() {
        isFullscreen = false;
        fullscreenOverlay.classList.add('hidden');
        
        // Sync input content back to normal chat
        userInput.value = fullscreenInput.value;
        
        // Focus back on normal input
        setTimeout(() => {
            userInput.focus();
        }, 100);
        
        // Restore state
        restoreFullscreenState();
        
        // Show exit indicator
        showFullscreenIndicator('Полноэкранный режим отключен');
        
        // Update button icon to indicate normal state
        updateFullscreenButtonState(false);
    }
    
    // Show fullscreen state indicator
    function showFullscreenIndicator(message) {
        // Remove existing indicator
        const existingIndicator = document.querySelector('.fullscreen-indicator');
        if (existingIndicator) {
            existingIndicator.remove();
        }
        
        // Create new indicator
        const indicator = document.createElement('div');
        indicator.className = 'fullscreen-indicator';
        indicator.textContent = message;
        document.body.appendChild(indicator);
        
        // Show indicator
        setTimeout(() => {
            indicator.classList.add('show');
        }, 100);
        
        // Hide and remove indicator after 3 seconds
        setTimeout(() => {
            indicator.classList.remove('show');
            setTimeout(() => {
                if (indicator.parentNode) {
                    indicator.parentNode.removeChild(indicator);
                }
            }, 300);
        }, 3000);
    }
    
    // Update fullscreen button state
    function updateFullscreenButtonState(isFullscreenActive) {
        const button = fullscreenChatBtn;
        if (isFullscreenActive) {
            button.classList.add('bg-blue-600', 'text-white');
            button.title = 'Выйти из полноэкранного режима (Esc)';
        } else {
            button.classList.remove('bg-blue-600', 'text-white');
            button.title = 'Полноэкранный режим (F11)';
        }
    }
    
    // Sync messages from normal chat to fullscreen
    function syncMessagesToFullscreen() {
        fullscreenMessages.innerHTML = chatMessages.innerHTML;
        
        // Re-add event listeners to copy buttons in fullscreen
        fullscreenMessages.querySelectorAll('.copy-button').forEach(button => {
            button.addEventListener('click', async () => {
                const code = button.parentElement.querySelector('code').textContent;
                try {
                    await navigator.clipboard.writeText(code);
                    button.classList.add('copied');
                    button.innerHTML = `
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    `;
                    setTimeout(() => {
                        button.classList.remove('copied');
                        button.innerHTML = `
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"></path>
                                <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z"></path>
                            </svg>
                        `;
                    }, 2000);
                } catch (err) {
                    console.error('Failed to copy code:', err);
                }
            });
        });
    }
    
    // Sync messages from fullscreen back to normal chat
    function syncMessagesFromFullscreen() {
        chatMessages.innerHTML = fullscreenMessages.innerHTML;
        
        // Re-add event listeners to copy buttons in normal chat
        chatMessages.querySelectorAll('.copy-button').forEach(button => {
            addCopyButtonEventListener(button);
        });
    }
    
    // Helper function to add copy button event listener
    function addCopyButtonEventListener(button) {
        button.addEventListener('click', async () => {
            const code = button.parentElement.querySelector('code').textContent;
            try {
                await navigator.clipboard.writeText(code);
                button.classList.add('copied');
                button.innerHTML = `
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                `;
                setTimeout(() => {
                    button.classList.remove('copied');
                    button.innerHTML = `
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"></path>
                            <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z"></path>
                        </svg>
                    `;
                }, 2000);
            } catch (err) {
                console.error('Failed to copy code:', err);
            }
        });
    }
    
    // Save fullscreen state
    function saveFullscreenState() {
        const state = {
            scrollPosition: chatMessages.scrollTop,
            inputValue: userInput.value,
            timestamp: Date.now()
        };
        sessionStorage.setItem('fullscreenState', JSON.stringify(state));
    }
    
    // Restore fullscreen state
    function restoreFullscreenState() {
        try {
            const state = JSON.parse(sessionStorage.getItem('fullscreenState'));
            if (state) {
                // Restore scroll position
                setTimeout(() => {
                    chatMessages.scrollTop = state.scrollPosition;
                }, 100);
            }
        } catch (err) {
            console.error('Failed to restore fullscreen state:', err);
        }
    }
    
    // Canvas functionality
    function copyAllCode() {
        if (!currentCode || !currentCode.trim()) {
            notifications.show('Нет кода для копирования', 'warning');
            return;
        }
        
        utils.copyToClipboard(currentCode).then(success => {
            if (success) {
                // Update button state
                const originalText = copyAllCodeBtn.textContent;
                const originalClasses = copyAllCodeBtn.className;
                
                copyAllCodeBtn.textContent = 'Скопировано!';
                copyAllCodeBtn.className = originalClasses.replace('bg-blue-600', 'bg-green-600').replace('hover:bg-blue-700', 'hover:bg-green-700');
                
                // Show success notification
                notifications.show('Код успешно скопирован в буфер обмена', 'success');
                
                // Reset button after 2 seconds
                setTimeout(() => {
                    copyAllCodeBtn.textContent = originalText;
                    copyAllCodeBtn.className = originalClasses;
                }, 2000);
            } else {
                notifications.show('Ошибка при копировании кода', 'error');
            }
        });
    }
    
    function saveCode() {
        if (!currentCode || !currentCode.trim()) {
            notifications.show('Нет кода для сохранения', 'warning');
            return;
        }
        
        // Show file format selection modal
        showFileFormatModal();
    }
    
    // Show file format selection modal
    function showFileFormatModal() {
        const modal = createFileFormatModal();
        document.body.appendChild(modal);
        
        // Focus on the modal
        setTimeout(() => {
            const firstInput = modal.querySelector('input[type="radio"]:checked');
            if (firstInput) firstInput.focus();
        }, 100);
    }
    
    // Create file format selection modal
    function createFileFormatModal() {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        modal.id = 'file-format-modal';
        
        const detectedExtension = getFileExtension(currentLanguage);
        const suggestedFilename = getSuggestedFilename(currentLanguage);
        
        const fileFormats = [
            { ext: 'py', name: 'Python', mime: 'text/x-python' },
            { ext: 'js', name: 'JavaScript', mime: 'text/javascript' },
            { ext: 'ts', name: 'TypeScript', mime: 'text/typescript' },
            { ext: 'html', name: 'HTML', mime: 'text/html' },
            { ext: 'css', name: 'CSS', mime: 'text/css' },
            { ext: 'json', name: 'JSON', mime: 'application/json' },
            { ext: 'xml', name: 'XML', mime: 'text/xml' },
            { ext: 'sql', name: 'SQL', mime: 'text/sql' },
            { ext: 'java', name: 'Java', mime: 'text/x-java' },
            { ext: 'cpp', name: 'C++', mime: 'text/x-c++src' },
            { ext: 'c', name: 'C', mime: 'text/x-csrc' },
            { ext: 'cs', name: 'C#', mime: 'text/x-csharp' },
            { ext: 'php', name: 'PHP', mime: 'text/x-php' },
            { ext: 'rb', name: 'Ruby', mime: 'text/x-ruby' },
            { ext: 'go', name: 'Go', mime: 'text/x-go' },
            { ext: 'rs', name: 'Rust', mime: 'text/x-rust' },
            { ext: 'swift', name: 'Swift', mime: 'text/x-swift' },
            { ext: 'kt', name: 'Kotlin', mime: 'text/x-kotlin' },
            { ext: 'scala', name: 'Scala', mime: 'text/x-scala' },
            { ext: 'sh', name: 'Shell Script', mime: 'text/x-shellscript' },
            { ext: 'yml', name: 'YAML', mime: 'text/yaml' },
            { ext: 'toml', name: 'TOML', mime: 'text/toml' },
            { ext: 'md', name: 'Markdown', mime: 'text/markdown' },
            { ext: 'txt', name: 'Plain Text', mime: 'text/plain' }
        ];
        
        modal.innerHTML = `
            <div class="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4 max-h-96 overflow-y-auto">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-white">Сохранить код</h3>
                    <button id="close-modal" class="text-gray-400 hover:text-white">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                </div>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-300 mb-2">Имя файла:</label>
                    <input type="text" id="filename-input" value="${suggestedFilename}" 
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-300 mb-2">Формат файла:</label>
                    <div class="max-h-48 overflow-y-auto space-y-2">
                        ${fileFormats.map(format => `
                            <label class="flex items-center space-x-3 p-2 rounded hover:bg-gray-700 cursor-pointer">
                                <input type="radio" name="file-format" value="${format.ext}" 
                                       ${format.ext === detectedExtension ? 'checked' : ''}
                                       class="text-blue-500 focus:ring-blue-500">
                                <span class="text-white">.${format.ext}</span>
                                <span class="text-gray-400 text-sm">${format.name}</span>
                            </label>
                        `).join('')}
                    </div>
                </div>
                
                <div class="flex space-x-3">
                    <button id="save-file-btn" class="flex-1 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded transition-colors">
                        Сохранить
                    </button>
                    <button id="cancel-save-btn" class="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded transition-colors">
                        Отмена
                    </button>
                </div>
            </div>
        `;
        
        // Add event listeners
        modal.querySelector('#close-modal').addEventListener('click', () => closeFileFormatModal());
        modal.querySelector('#cancel-save-btn').addEventListener('click', () => closeFileFormatModal());
        modal.querySelector('#save-file-btn').addEventListener('click', () => performFileSave());
        
        // Close modal on background click
        modal.addEventListener('click', (e) => {
            if (e.target === modal) closeFileFormatModal();
        });
        
        // Handle escape key
        const handleEscape = (e) => {
            if (e.key === 'Escape') {
                closeFileFormatModal();
                document.removeEventListener('keydown', handleEscape);
            }
        };
        document.addEventListener('keydown', handleEscape);
        
        // Update filename when format changes
        modal.querySelectorAll('input[name="file-format"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                const filenameInput = modal.querySelector('#filename-input');
                const currentName = filenameInput.value;
                const nameWithoutExt = currentName.includes('.') ? 
                    currentName.substring(0, currentName.lastIndexOf('.')) : currentName;
                filenameInput.value = `${nameWithoutExt}.${e.target.value}`;
            });
        });
        
        return modal;
    }
    
    // Close file format modal
    function closeFileFormatModal() {
        const modal = document.getElementById('file-format-modal');
        if (modal) {
            modal.remove();
        }
    }
    
    // Perform file save with selected format
    function performFileSave() {
        const modal = document.getElementById('file-format-modal');
        const selectedFormat = modal.querySelector('input[name="file-format"]:checked');
        const filenameInput = modal.querySelector('#filename-input');
        
        if (!selectedFormat || !filenameInput.value.trim()) {
            notifications.show('Выберите формат файла и введите имя', 'warning');
            return;
        }
        
        const filename = filenameInput.value.trim();
        const extension = selectedFormat.value;
        const mimeType = getMimeType(extension);
        
        try {
            // Create and download file
            const blob = new Blob([currentCode], { type: mimeType });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            a.style.display = 'none';
            
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            // Show success notification
            notifications.show(`Файл "${filename}" успешно сохранен`, 'success');
            
            // Update save button state temporarily
            const originalText = saveCodeBtn.textContent;
            const originalClasses = saveCodeBtn.className;
            
            saveCodeBtn.textContent = 'Сохранено!';
            saveCodeBtn.className = originalClasses.replace('bg-green-600', 'bg-blue-600').replace('hover:bg-green-700', 'hover:bg-blue-700');
            
            setTimeout(() => {
                saveCodeBtn.textContent = originalText;
                saveCodeBtn.className = originalClasses;
            }, 2000);
            
        } catch (error) {
            console.error('Failed to save file:', error);
            notifications.show('Ошибка при сохранении файла', 'error');
        }
        
        closeFileFormatModal();
    }
    
    // Get suggested filename based on language
    function getSuggestedFilename(language) {
        const baseName = 'code';
        const extension = getFileExtension(language);
        return `${baseName}.${extension}`;
    }
    
    // Get MIME type for file extension
    function getMimeType(extension) {
        const mimeTypes = {
            'py': 'text/x-python',
            'js': 'text/javascript',
            'ts': 'text/typescript',
            'html': 'text/html',
            'css': 'text/css',
            'json': 'application/json',
            'xml': 'text/xml',
            'sql': 'text/sql',
            'java': 'text/x-java',
            'cpp': 'text/x-c++src',
            'c': 'text/x-csrc',
            'cs': 'text/x-csharp',
            'php': 'text/x-php',
            'rb': 'text/x-ruby',
            'go': 'text/x-go',
            'rs': 'text/x-rust',
            'swift': 'text/x-swift',
            'kt': 'text/x-kotlin',
            'scala': 'text/x-scala',
            'sh': 'text/x-shellscript',
            'yml': 'text/yaml',
            'yaml': 'text/yaml',
            'toml': 'text/toml',
            'md': 'text/markdown',
            'txt': 'text/plain'
        };
        return mimeTypes[extension.toLowerCase()] || 'text/plain';
    }
    
    // Utility functions
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    function getFileExtension(language) {
        const extensions = {
            'javascript': 'js',
            'python': 'py',
            'html': 'html',
            'css': 'css',
            'json': 'json',
            'xml': 'xml',
            'sql': 'sql',
            'bash': 'sh',
            'shell': 'sh',
            'typescript': 'ts',
            'java': 'java',
            'cpp': 'cpp',
            'c': 'c',
            'csharp': 'cs',
            'php': 'php',
            'ruby': 'rb',
            'go': 'go',
            'rust': 'rs',
            'swift': 'swift',
            'kotlin': 'kt',
            'scala': 'scala',
            'r': 'r',
            'matlab': 'm',
            'perl': 'pl',
            'lua': 'lua',
            'dart': 'dart',
            'yaml': 'yml',
            'yml': 'yml',
            'toml': 'toml',
            'ini': 'ini',
            'dockerfile': 'dockerfile',
            'makefile': 'makefile',
            'markdown': 'md',
            'md': 'md',
            'tex': 'tex',
            'latex': 'tex',
            'vim': 'vim',
            'powershell': 'ps1',
            'batch': 'bat',
            'cmd': 'bat',
            'vb': 'vb',
            'vbnet': 'vb',
            'fsharp': 'fs',
            'clojure': 'clj',
            'erlang': 'erl',
            'elixir': 'ex',
            'haskell': 'hs',
            'ocaml': 'ml',
            'scheme': 'scm',
            'lisp': 'lisp',
            'prolog': 'pl',
            'fortran': 'f90',
            'cobol': 'cob',
            'pascal': 'pas',
            'ada': 'ada',
            'assembly': 'asm',
            'nasm': 'asm',
            'plaintext': 'txt',
            'text': 'txt'
        };
        return extensions[language.toLowerCase()] || 'txt';
    }
});

// Enhanced error handling system
class ErrorHandler {
        constructor() {
            this.errorQueue = [];
            this.maxErrors = 10;
            this.errorTypes = {
                NETWORK: 'network',
                API: 'api',
                VALIDATION: 'validation',
                SYSTEM: 'system'
            };
        }

        // Log error with context
        logError(error, context = {}) {
            const errorEntry = {
                timestamp: new Date().toISOString(),
                error: {
                    name: error.name,
                    message: error.message,
                    stack: error.stack
                },
                context,
                id: this.generateErrorId()
            };

            this.errorQueue.push(errorEntry);
            
            // Keep only recent errors
            if (this.errorQueue.length > this.maxErrors) {
                this.errorQueue.shift();
            }

            console.error('Error logged:', errorEntry);
            return errorEntry.id;
        }

        // Generate unique error ID
        generateErrorId() {
            return 'err_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }

        // Get error statistics
        getErrorStats() {
            const stats = {
                total: this.errorQueue.length,
                byType: {},
                recent: this.errorQueue.slice(-5)
            };

            this.errorQueue.forEach(entry => {
                const type = this.classifyError(entry.error);
                stats.byType[type] = (stats.byType[type] || 0) + 1;
            });

            return stats;
        }

        // Classify error type
        classifyError(error) {
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                return this.errorTypes.NETWORK;
            }
            if (error.message.includes('API') || error.message.includes('OpenAI')) {
                return this.errorTypes.API;
            }
            if (error.message.includes('validation') || error.message.includes('invalid')) {
                return this.errorTypes.VALIDATION;
            }
            return this.errorTypes.SYSTEM;
        }

        // Clear error queue
        clearErrors() {
            this.errorQueue = [];
        }
    }

    // Initialize error handler
    const errorHandler = new ErrorHandler();

    // Enhanced notification system with error tracking
    class EnhancedNotificationSystem extends NotificationSystem {
        constructor() {
            super();
            this.errorNotifications = new Map();
            this.retryTimers = new Map();
        }

        // Show error notification with retry option
        showError(title, message, options = {}) {
            const {
                type = 'error',
                duration = 0,
                retryCallback = null,
                retryAfter = 0,
                errorId = null
            } = options;

            // Remove existing error notification if it exists
            if (errorId && this.errorNotifications.has(errorId)) {
                this.remove(this.errorNotifications.get(errorId));
            }

            const notification = this.show(`${title}: ${message}`, type, duration);
            
            if (errorId) {
                this.errorNotifications.set(errorId, notification);
            }

            // Add retry functionality if callback provided
            if (retryCallback) {
                this.addRetryButton(notification, retryCallback, retryAfter);
            }

            return notification;
        }

        // Add retry button to notification
        addRetryButton(notification, retryCallback, retryAfter = 0) {
            const retryButton = document.createElement('button');
            retryButton.className = 'retry-btn bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm mt-2 flex items-center space-x-1';
            retryButton.innerHTML = `
                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"></path>
                </svg>
                <span>Повторить${retryAfter > 0 ? ` (${retryAfter}с)` : ''}</span>
            `;

            // Disable button initially if there's a delay
            if (retryAfter > 0) {
                retryButton.disabled = true;
                this.startRetryCountdown(retryButton, retryAfter);
            }

            retryButton.addEventListener('click', () => {
                this.remove(notification);
                retryCallback();
            });

            notification.querySelector('.notification-message').appendChild(retryButton);
        }

        // Start retry countdown
        startRetryCountdown(button, seconds) {
            let remaining = seconds;
            const span = button.querySelector('span');
            
            const timer = setInterval(() => {
                remaining--;
                span.textContent = `Повторить (${remaining}с)`;
                
                if (remaining <= 0) {
                    clearInterval(timer);
                    button.disabled = false;
                    span.textContent = 'Повторить';
                }
            }, 1000);

            this.retryTimers.set(button, timer);
        }

        // Override remove to clean up timers
        remove(notification) {
            // Clean up any retry timers
            const retryButton = notification.querySelector('.retry-btn');
            if (retryButton && this.retryTimers.has(retryButton)) {
                clearInterval(this.retryTimers.get(retryButton));
                this.retryTimers.delete(retryButton);
            }

            super.remove(notification);
        }

        // Show connection status
        showConnectionStatus(isOnline) {
            const statusNotification = this.show(
                isOnline ? 'Соединение восстановлено' : 'Соединение потеряно',
                isOnline ? 'success' : 'error',
                isOnline ? 3000 : 0
            );

            if (!isOnline) {
                // Add manual retry button for offline state
                this.addRetryButton(statusNotification, () => {
                    location.reload();
                });
            }

            return statusNotification;
        }
    }

    // Replace the existing notification system
    const enhancedNotifications = new EnhancedNotificationSystem();
    
    // Update global reference
    window.AIIDENotifications = enhancedNotifications;

    // Enhanced API error handling
    function handleEnhancedAPIError(error, originalMessage = '') {
        const errorId = errorHandler.logError(error, {
            originalMessage,
            endpoint: 'chat',
            timestamp: new Date().toISOString()
        });

        let title = 'Ошибка API';
        let message = error.message;
        let retryAfter = 0;
        let retryCallback = null;

        switch (error.status) {
            case 400:
                title = 'Некорректный запрос';
                message = 'Проверьте введенное сообщение';
                break;
            case 401:
                title = 'Ошибка авторизации';
                message = 'Проблема с API ключом. Обратитесь к администратору.';
                break;
            case 403:
                title = 'Доступ запрещен';
                message = 'Недостаточно прав для выполнения операции';
                break;
            case 404:
                title = 'Сервис не найден';
                message = 'API endpoint недоступен';
                break;
            case 429:
                title = 'Превышен лимит запросов';
                message = 'Слишком много запросов. Автоматическая попытка через некоторое время.';
                retryAfter = error.data?.retry_after || 60;
                retryCallback = () => retryMessage(originalMessage);
                break;
            case 500:
            case 502:
            case 503:
            case 504:
                title = 'Ошибка сервера';
                message = 'Сервер временно недоступен. Автоматическая попытка через некоторое время.';
                retryAfter = error.data?.retry_after || 30;
                retryCallback = () => retryMessage(originalMessage);
                break;
            default:
                if (error.status >= 500) {
                    title = 'Ошибка сервера';
                    message = 'Внутренняя ошибка сервера';
                    retryAfter = 10;
                    retryCallback = () => retryMessage(originalMessage);
                }
        }

        // Show enhanced error notification
        enhancedNotifications.showError(title, message, {
            type: error.status >= 500 ? 'error' : 'warning',
            retryCallback,
            retryAfter,
            errorId
        });

        // Add error message to chat
        addSystemMessage(`❌ ${title}: ${message}`, 'error');
    }

    // Enhanced network error handling
    function handleEnhancedNetworkError(error, originalMessage = '') {
        const errorId = errorHandler.logError(error, {
            originalMessage,
            type: 'network',
            timestamp: new Date().toISOString()
        });

        let title = 'Ошибка сети';
        let message = 'Проверьте подключение к интернету';
        let retryAfter = 5;

        if (error.name === 'AbortError') {
            title = 'Запрос отменен';
            message = 'Запрос был отменен пользователем';
            retryAfter = 0;
        } else if (error.message.includes('timeout')) {
            title = 'Превышено время ожидания';
            message = 'Сервер не отвечает. Проверьте подключение.';
            retryAfter = 10;
        } else if (error.message.includes('fetch')) {
            title = 'Ошибка подключения';
            message = 'Не удается подключиться к серверу';
            retryAfter = 5;
        }

        // Show enhanced error notification
        enhancedNotifications.showError(title, message, {
            type: 'error',
            retryCallback: originalMessage ? () => retryMessage(originalMessage) : null,
            retryAfter,
            errorId
        });

        // Add error message to chat
        addSystemMessage(`🌐 ${title}: ${message}`, 'error');
    }

    // Add system message to chat
    function addSystemMessage(content, type = 'info') {
        const messageDiv = document.createElement('div');
        messageDiv.className = `system-message system-${type} bg-gray-800 border-l-4 border-${type === 'error' ? 'red' : type === 'warning' ? 'yellow' : 'blue'}-500 p-3 my-2 rounded`;
        
        const timestamp = new Date().toLocaleTimeString('ru-RU', {
            hour: '2-digit',
            minute: '2-digit'
        });

        messageDiv.innerHTML = `
            <div class="flex items-start space-x-2">
                <div class="system-icon text-${type === 'error' ? 'red' : type === 'warning' ? 'yellow' : 'blue'}-400">
                    ${type === 'error' ? '⚠️' : type === 'warning' ? '⚡' : 'ℹ️'}
                </div>
                <div class="flex-1">
                    <div class="text-sm text-gray-300">${content}</div>
                    <div class="text-xs text-gray-500 mt-1">Система • ${timestamp}</div>
                </div>
            </div>
        `;

        chatMessages.appendChild(messageDiv);
        fullscreenMessages.appendChild(messageDiv.cloneNode(true));
        scrollToBottom();
    }

    // Enhanced retry message function
    function retryMessage(message) {
        if (!message) return;
        
        const input = isFullscreen ? fullscreenInput : userInput;
        input.value = message;
        
        // Clear any existing error notifications
        enhancedNotifications.clear();
        
        // Add retry indicator
        addSystemMessage(`🔄 Повторная попытка отправки сообщения...`, 'info');
        
        sendMessage(isFullscreen);
    }

    // Connection monitoring with enhanced error handling
    function setupEnhancedConnectionMonitoring() {
        let wasOnline = navigator.onLine;
        
        const checkConnection = () => {
            const isCurrentlyOnline = navigator.onLine;
            
            if (wasOnline !== isCurrentlyOnline) {
                wasOnline = isCurrentlyOnline;
                enhancedNotifications.showConnectionStatus(isCurrentlyOnline);
                
                if (isCurrentlyOnline) {
                    // Connection restored - retry failed requests
                    addSystemMessage('🟢 Соединение восстановлено. Можно продолжить работу.', 'info');
                } else {
                    // Connection lost
                    addSystemMessage('🔴 Соединение потеряно. Проверьте подключение к интернету.', 'error');
                }
            }
        };

        // Monitor connection events
        window.addEventListener('online', checkConnection);
        window.addEventListener('offline', checkConnection);
        
        // Periodic connection check
        setInterval(checkConnection, 5000);
        
        // Initial check
        checkConnection();
    }

    // Error recovery system
    class ErrorRecoverySystem {
        constructor() {
            this.recoveryStrategies = new Map();
            this.setupDefaultStrategies();
        }

        setupDefaultStrategies() {
            // Network error recovery
            this.recoveryStrategies.set('network', {
                maxRetries: 3,
                retryDelay: 2000,
                backoffMultiplier: 2,
                recovery: async (error, context) => {
                    // Wait for connection to be restored
                    if (!navigator.onLine) {
                        await this.waitForConnection();
                    }
                    
                    // Retry the original request
                    if (context.originalMessage) {
                        return retryMessage(context.originalMessage);
                    }
                }
            });

            // API error recovery
            this.recoveryStrategies.set('api', {
                maxRetries: 2,
                retryDelay: 5000,
                backoffMultiplier: 2,
                recovery: async (error, context) => {
                    // For 5xx errors, wait and retry
                    if (error.status >= 500) {
                        await this.delay(5000);
                        if (context.originalMessage) {
                            return retryMessage(context.originalMessage);
                        }
                    }
                    
                    // For 429 (rate limit), respect retry-after header
                    if (error.status === 429) {
                        const retryAfter = error.data?.retry_after || 60;
                        await this.delay(retryAfter * 1000);
                        if (context.originalMessage) {
                            return retryMessage(context.originalMessage);
                        }
                    }
                }
            });
        }

        async waitForConnection(timeout = 30000) {
            return new Promise((resolve, reject) => {
                if (navigator.onLine) {
                    resolve();
                    return;
                }

                const timeoutId = setTimeout(() => {
                    window.removeEventListener('online', onlineHandler);
                    reject(new Error('Connection timeout'));
                }, timeout);

                const onlineHandler = () => {
                    clearTimeout(timeoutId);
                    window.removeEventListener('online', onlineHandler);
                    resolve();
                };

                window.addEventListener('online', onlineHandler);
            });
        }

        delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        async attemptRecovery(errorType, error, context) {
            const strategy = this.recoveryStrategies.get(errorType);
            if (!strategy) return false;

            try {
                await strategy.recovery(error, context);
                return true;
            } catch (recoveryError) {
                console.error('Recovery failed:', recoveryError);
                return false;
            }
        }
    }

    // Initialize error recovery system
    const errorRecovery = new ErrorRecoverySystem();

    // Setup enhanced connection monitoring
    setupEnhancedConnectionMonitoring();

    // Update the existing sendMessage function to use enhanced error handling
    const originalSendMessage = sendMessage;
    
    // Override sendMessage with enhanced error handling
    window.sendMessage = api.debounce('sendMessage', async function(isFullscreen = false) {
        const input = isFullscreen ? fullscreenInput : userInput;
        const message = input.value.trim();
        
        if (!message || sendButton.disabled) return;
        
        // Validate message before sending
        const validation = utils.validateMessage(message);
        if (!validation.isValid) {
            validation.errors.forEach(error => {
                enhancedNotifications.show(error, 'error', 4000);
            });
            return;
        }
        
        // Add user message to chat immediately
        addMessage(message, 'user');
        input.value = '';
        setLoadingState(true);
        
        try {
            performanceMonitor.start('Отправка сообщения');
            
            // Send message to API with retry logic
            const data = await api.sendMessage(message, true);
            
            performanceMonitor.end('Отправка сообщения');
            
            // Handle successful response
            if (data.response) {
                addMessage(data.response, 'assistant', data.code, data.language);
                
                // Update canvas if code is present
                if (data.code) {
                    updateCanvas(data.code, data.language);
                    enhancedNotifications.show('Код обновлен в холсте', 'success', 3000);
                }
                
                // Show notification for multiple code blocks
                if (data.code_blocks && data.code_blocks.length > 1) {
                    enhancedNotifications.show(`Получено ${data.code_blocks.length} блоков кода`, 'info', 3000);
                }
                
                // Handle fallback responses
                if (data.status === 'fallback') {
                    enhancedNotifications.show('ИИ недоступен. Показан резервный ответ.', 'warning', 5000);
                    addSystemMessage('⚠️ ИИ-сервис временно недоступен. Показан резервный ответ.', 'warning');
                }
            }
            
        } catch (error) {
            console.error('Chat error:', error);
            performanceMonitor.end('Отправка сообщения');
            
            if (error instanceof APIError) {
                // Handle specific API errors with enhanced system
                handleEnhancedAPIError(error, message);
            } else {
                // Handle network/unknown errors with enhanced system
                handleEnhancedNetworkError(error, message);
            }
        } finally {
            setLoadingState(false);
        }
    }, 300);

    // Export enhanced error handling for global use
    window.AIIDEErrorHandler = errorHandler;
    window.AIIDEErrorRecovery = errorRecovery;
    
    console.log('Enhanced error handling system initialized');