from flask import Flask, render_template, request, jsonify
import os
from datetime import datetime
from dataclasses import dataclass
from typing import List, Optional
from dotenv import load_dotenv
from services.openrouter_service import OpenRouterService, CodeBlock

# Загружаем переменные окружения
load_dotenv()

@dataclass
class Message:
    role: str  # 'user' | 'assistant'
    content: str
    timestamp: datetime
    code: Optional[str] = None
    language: Optional[str] = None

class Conversation:
    def __init__(self):
        self.messages: List[Message] = []
        self.created_at: datetime = datetime.now()
    
    def add_message(self, message: Message):
        self.messages.append(message)
    
    def get_openai_format(self) -> List[dict]:
        """Конвертация в формат OpenAI API"""
        return [{"role": msg.role, "content": msg.content} for msg in self.messages]

class AIIDEApp:
    def __init__(self):
        self.app = Flask(__name__)
        self.app.secret_key = os.getenv('SECRET_KEY', os.urandom(24))
        
        # Инициализация OpenRouter сервиса с обработкой ошибок
        try:
            self.openrouter_service = OpenRouterService()
        except ValueError as e:
            print(f"Ошибка инициализации OpenRouter: {e}")
            # Создаем заглушку для разработки
            self.openrouter_service = None
        
        self.conversation = Conversation()
        self._setup_routes()
        self._setup_error_handlers()
    
    def _setup_routes(self):
        """Настройка маршрутов"""
        self.app.add_url_rule('/', 'index', self.index)
        self.app.add_url_rule('/api/chat', 'chat', self.chat, methods=['POST'])
        self.app.add_url_rule('/api/history', 'history', self.get_history, methods=['GET'])
        self.app.add_url_rule('/api/clear-history', 'clear_history', self.clear_history, methods=['POST'])
        self.app.add_url_rule('/api/status', 'status', self.get_status, methods=['GET'])
    
    def _setup_error_handlers(self):
        """Настройка обработчиков ошибок"""
        @self.app.errorhandler(404)
        def not_found(error):
            if request.path.startswith('/api/'):
                return jsonify({
                    'error': 'Not found',
                    'message': 'Запрашиваемый API endpoint не найден',
                    'code': 'ENDPOINT_NOT_FOUND'
                }), 404
            else:
                return render_template('errors/404.html'), 404
        
        @self.app.errorhandler(500)
        def internal_error(error):
            if request.path.startswith('/api/'):
                return jsonify({
                    'error': 'Internal server error',
                    'message': 'Произошла внутренняя ошибка сервера',
                    'code': 'INTERNAL_ERROR'
                }), 500
            else:
                return render_template('errors/500.html'), 500
        
        @self.app.errorhandler(503)
        def service_unavailable(error):
            return jsonify({
                'error': 'Service unavailable',
                'message': 'Сервис временно недоступен. Попробуйте позже.',
                'code': 'SERVICE_UNAVAILABLE',
                'retry_after': 30
            }), 503
        
        @self.app.errorhandler(429)
        def rate_limit_exceeded(error):
            return jsonify({
                'error': 'Rate limit exceeded',
                'message': 'Слишком много запросов. Подождите немного.',
                'code': 'RATE_LIMIT_EXCEEDED',
                'retry_after': 60
            }), 429
        
        @self.app.errorhandler(Exception)
        def handle_exception(error):
            import traceback
            error_trace = traceback.format_exc()
            print(f"Unhandled exception: {error_trace}")
            
            # Логируем ошибку с контекстом
            error_context = {
                'path': request.path,
                'method': request.method,
                'user_agent': request.headers.get('User-Agent', ''),
                'timestamp': datetime.now().isoformat(),
                'trace': error_trace
            }
            
            # Обработка ошибок OpenAI API
            if 'openai' in str(type(error)).lower() or 'OpenAI' in str(error):
                error_info = {
                    'error': 'AI service unavailable',
                    'message': 'Сервис ИИ временно недоступен. Попробуйте позже.',
                    'code': 'AI_SERVICE_ERROR',
                    'retry_after': 30,
                    'context': error_context if self.app.debug else None
                }
                
                if request.path.startswith('/api/'):
                    return jsonify(error_info), 503
                else:
                    return render_template('errors/500.html'), 500
            
            # Обработка сетевых ошибок
            if 'connection' in str(error).lower() or 'timeout' in str(error).lower():
                error_info = {
                    'error': 'Network error',
                    'message': 'Ошибка сети. Проверьте подключение.',
                    'code': 'NETWORK_ERROR',
                    'retry_after': 10,
                    'context': error_context if self.app.debug else None
                }
                
                if request.path.startswith('/api/'):
                    return jsonify(error_info), 503
                else:
                    return render_template('errors/500.html'), 500
            
            # Обработка ошибок валидации
            if 'validation' in str(error).lower() or 'invalid' in str(error).lower():
                error_info = {
                    'error': 'Validation error',
                    'message': 'Ошибка валидации данных',
                    'code': 'VALIDATION_ERROR',
                    'context': error_context if self.app.debug else None
                }
                
                if request.path.startswith('/api/'):
                    return jsonify(error_info), 400
                else:
                    return render_template('errors/500.html'), 500
            
            # Общая обработка ошибок
            error_info = {
                'error': 'Unexpected error',
                'message': 'Произошла неожиданная ошибка',
                'code': 'UNEXPECTED_ERROR',
                'context': error_context if self.app.debug else None
            }
            
            if request.path.startswith('/api/'):
                return jsonify(error_info), 500
            else:
                return render_template('errors/500.html'), 500

    def index(self):
        """Главная страница"""
        return render_template('index.html')
    
    def chat(self):
        """API endpoint для отправки сообщений ИИ с graceful degradation"""
        try:
            # Проверяем доступность сервиса
            if not self.openrouter_service:
                return jsonify({
                    'error': 'Service unavailable',
                    'message': 'OpenRouter сервис не инициализирован. Проверьте API ключ.',
                    'code': 'SERVICE_NOT_INITIALIZED',
                    'fallback_available': False
                }), 503
            
            # Проверяем валидность JSON
            try:
                data = request.get_json()
            except Exception:
                return jsonify({
                    'error': 'Bad request',
                    'message': 'Невалидный JSON',
                    'code': 'INVALID_JSON'
                }), 400
            
            if not data or 'message' not in data:
                return jsonify({
                    'error': 'Bad request',
                    'message': 'Сообщение не предоставлено',
                    'code': 'MESSAGE_REQUIRED'
                }), 400
            
            user_message = data['message'].strip()
            if not user_message:
                return jsonify({
                    'error': 'Bad request',
                    'message': 'Сообщение не может быть пустым',
                    'code': 'MESSAGE_EMPTY'
                }), 400
            
            # Проверяем длину сообщения
            if len(user_message) > 4000:
                return jsonify({
                    'error': 'Bad request',
                    'message': 'Сообщение слишком длинное (максимум 4000 символов)',
                    'code': 'MESSAGE_TOO_LONG'
                }), 400
            
            # Добавляем сообщение пользователя
            user_msg = Message(
                role='user',
                content=user_message,
                timestamp=datetime.now()
            )
            self.conversation.add_message(user_msg)
            
            try:
                # Проверяем доступность API перед запросом
                if not self.openrouter_service.validate_api_connection():
                    return self._handle_service_unavailable(user_message)
                
                # Получаем ответ от ИИ с извлечением кода
                ai_response, code_blocks = self.openrouter_service.get_full_code_response(
                    user_message, 
                    self.conversation.get_openai_format()[:-1]  # Исключаем последнее сообщение пользователя
                )
                
                # Определяем основной блок кода (если есть)
                main_code = None
                main_language = None
                if code_blocks:
                    # Берем самый большой блок кода как основной
                    main_block = max(code_blocks, key=lambda x: len(x.content))
                    main_code = main_block.content
                    main_language = main_block.language
                
                # Добавляем ответ ИИ
                ai_msg = Message(
                    role='assistant',
                    content=ai_response,
                    timestamp=datetime.now(),
                    code=main_code,
                    language=main_language
                )
                self.conversation.add_message(ai_msg)
                
                return jsonify({
                    'response': ai_response,
                    'code': main_code,
                    'language': main_language,
                    'code_blocks': [
                        {
                            'content': block.content,
                            'language': block.language,
                            'filename': block.filename
                        } for block in code_blocks
                    ],
                    'timestamp': ai_msg.timestamp.isoformat(),
                    'status': 'success'
                })
                
            except Exception as api_error:
                # Graceful degradation - возвращаем fallback ответ
                return self._handle_api_error(user_message, str(api_error))
            
        except Exception as e:
            return jsonify({
                'error': 'Processing error',
                'message': f'Ошибка обработки запроса: {str(e)}',
                'code': 'PROCESSING_ERROR'
            }), 500
    
    def _handle_service_unavailable(self, user_message: str):
        """Обработка недоступности сервиса с fallback ответом"""
        fallback_response = self._generate_fallback_response(user_message)
        
        # Добавляем fallback ответ в историю
        ai_msg = Message(
            role='assistant',
            content=fallback_response,
            timestamp=datetime.now()
        )
        self.conversation.add_message(ai_msg)
        
        return jsonify({
            'response': fallback_response,
            'code': None,
            'language': None,
            'code_blocks': [],
            'timestamp': ai_msg.timestamp.isoformat(),
            'status': 'fallback',
            'error': 'Service unavailable',
            'message': 'ИИ сервис недоступен. Показан fallback ответ.',
            'code': 'AI_SERVICE_UNAVAILABLE',
            'retry_after': 30
        }), 503
    
    def _handle_api_error(self, user_message: str, error_message: str):
        """Обработка ошибок API с fallback ответом"""
        fallback_response = self._generate_fallback_response(user_message)
        
        # Добавляем fallback ответ в историю
        ai_msg = Message(
            role='assistant',
            content=fallback_response,
            timestamp=datetime.now()
        )
        self.conversation.add_message(ai_msg)
        
        return jsonify({
            'response': fallback_response,
            'code': None,
            'language': None,
            'code_blocks': [],
            'timestamp': ai_msg.timestamp.isoformat(),
            'status': 'fallback',
            'error': 'API error',
            'message': f'Ошибка API: {error_message}. Показан fallback ответ.',
            'code': 'API_ERROR',
            'retry_after': 10
        }), 503
    
    def _generate_fallback_response(self, user_message: str) -> str:
        """Генерация fallback ответа когда ИИ недоступен"""
        # Простая эвристика для определения типа запроса
        message_lower = user_message.lower()
        
        if any(keyword in message_lower for keyword in ['код', 'code', 'функция', 'function', 'класс', 'class']):
            return """К сожалению, ИИ-сервис временно недоступен, и я не могу сгенерировать код прямо сейчас.

**Что вы можете сделать:**
- Попробуйте повторить запрос через несколько минут
- Проверьте подключение к интернету
- Обратитесь к документации по вашему языку программирования

Сервис будет восстановлен в ближайшее время."""
        
        elif any(keyword in message_lower for keyword in ['помощь', 'help', 'как', 'what', 'что']):
            return """Извините, ИИ-помощник временно недоступен.

**Возможные причины:**
- Проблемы с подключением к серверу
- Техническое обслуживание сервиса
- Превышение лимитов API

**Рекомендации:**
- Попробуйте обновить страницу
- Повторите запрос через несколько минут
- Проверьте статус сервиса

Мы работаем над восстановлением сервиса."""
        
        else:
            return """ИИ-помощник временно недоступен.

Я не могу обработать ваш запрос прямо сейчас из-за технических проблем. Пожалуйста, попробуйте позже.

**Статус:** Сервис недоступен  
**Ожидаемое время восстановления:** 5-10 минут

Приносим извинения за неудобства."""
    
    def get_history(self):
        """API endpoint для получения истории чата"""
        try:
            messages = []
            for msg in self.conversation.messages:
                messages.append({
                    'role': msg.role,
                    'content': msg.content,
                    'timestamp': msg.timestamp.isoformat(),
                    'code': msg.code,
                    'language': msg.language
                })
            
            return jsonify({
                'messages': messages,
                'created_at': self.conversation.created_at.isoformat()
            })
            
        except Exception as e:
            return jsonify({
                'error': 'Processing error',
                'message': f'Ошибка получения истории: {str(e)}'
            }), 500
    
    def clear_history(self):
        """API endpoint для очистки истории"""
        try:
            self.conversation = Conversation()
            return jsonify({
                'status': 'success',
                'message': 'История очищена'
            })
            
        except Exception as e:
            return jsonify({
                'error': 'Processing error',
                'message': f'Ошибка очистки истории: {str(e)}'
            }), 500
    
    def get_status(self):
        """API endpoint для получения статуса сервиса"""
        try:
            status = {
                'server': 'online',
                'timestamp': datetime.now().isoformat(),
                'version': '1.0.0',
                'features': {
                    'chat': True,
                    'code_generation': True,
                    'history': True,
                    'error_handling': True
                }
            }
            
            # Проверяем статус OpenRouter сервиса
            if self.openrouter_service:
                try:
                    openrouter_status = self.openrouter_service.get_service_status()
                    status['openrouter'] = openrouter_status
                except Exception as e:
                    status['openrouter'] = {
                        'api_key_configured': False,
                        'connection_available': False,
                        'errors': [f'Ошибка получения статуса: {str(e)}']
                    }
            else:
                status['openrouter'] = {
                    'api_key_configured': False,
                    'connection_available': False,
                    'errors': ['OpenRouter сервис не инициализирован']
                }
            
            # Проверяем статус истории
            try:
                status['conversation'] = {
                    'messages_count': len(self.conversation.messages),
                    'created_at': self.conversation.created_at.isoformat()
                }
            except Exception as e:
                status['conversation'] = {
                    'messages_count': 0,
                    'created_at': datetime.now().isoformat(),
                    'error': f'Ошибка получения статуса истории: {str(e)}'
                }
            
            return jsonify(status)
            
        except Exception as e:
            return jsonify({
                'error': 'Status check failed',
                'message': f'Ошибка получения статуса: {str(e)}',
                'server': 'error'
            }), 500
    
    def run(self, debug=True, **kwargs):
        """Запуск приложения"""
        self.app.run(debug=debug, **kwargs)

# Создание экземпляра приложения
ai_ide_app = AIIDEApp()
app = ai_ide_app.app

if __name__ == '__main__':
    ai_ide_app.run(debug=True)