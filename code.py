#!/usr/bin/env python3
import argparse

def remove_m3u_duplicates(input_file, output_file):
    """
    Удаляет дубликаты из M3U-плейлиста, сохраняя порядок первых вхождений.
    Дубликаты определяются по пути/URL медиаресурса.
    """
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    except FileNotFoundError:
        print(f"Ошибка: Файл {input_file} не найден")
        return
    except Exception as e:
        print(f"Ошибка чтения файла: {e}")
        return

    header = None
    entries = []
    seen_urls = set()
    duplicates_count = 0
    line_number = 0

    # Обработка заголовка
    if lines and lines[0].strip() == "#EXTM3U":
        header = lines[0]
        line_number += 1

    # Парсинг записей
    while line_number < len(lines):
        if line_number + 1 >= len(lines):
            print("Предупреждение: Обнаружена неполная запись в конце файла")
            break

        extinf = lines[line_number].strip()
        url = lines[line_number + 1].strip()
        
        if not extinf.startswith("#EXTINF"):
            print(f"Предупреждение: Неожиданный формат в строке {line_number+1}")
            line_number += 1
            continue

        # Проверка дубликатов
        if url not in seen_urls:
            entries.append((extinf, url))
            seen_urls.add(url)
        else:
            duplicates_count += 1
        
        line_number += 2

    # Запись результата
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            if header:
                f.write(header)
            for extinf, url in entries:
                f.write(f"{extinf}\n{url}\n")
        print(f"Успешно обработано: {len(entries)} записей")
        print(f"Удалено дубликатов: {duplicates_count}")
    except Exception as e:
        print(f"Ошибка записи файла: {e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Удаление дубликатов из M3U-плейлиста')
    parser.add_argument('input', help='Входной M3U-файл')
    parser.add_argument('output', help='Выходной M3U-файл без дубликатов')
    args = parser.parse_args()

    remove_m3u_duplicates(args.input, args.output)